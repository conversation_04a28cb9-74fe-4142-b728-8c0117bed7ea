import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import AstronomicalLayer from './AstronomicalLayer';
import DiurnalLayer from './DiurnalLayer';
// 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer
// 🔧 CISCO: Suppression des imports GPS/automatisation
// import { useLocation } from '../Context/LocationContext'; // SUPPRIMÉ
// import { useTime } from '../Context/TimeContext'; // SUPPRIMÉ
// import * as SunCalc from 'suncalc'; // SUPPRIMÉ
import './BackgroundController'; // 🔧 IMPORT: Contrôleur manuel

// 🔧 CISCO: Système de rotation supprimé - Background fixe pour éviter les changements automatiques

// 🔧 CISCO: Fonction supprimée - Background fixe pour éviter les changements automatiques

// 🔧 SYSTÈME DE PILOTAGE MANUEL SIMPLIFIÉ
// Types pour les modes de fond prédéfinis
type BackgroundMode = 
  | 'dawn'        // Aube
  | 'sunrise'     // Lever du soleil
  | 'morning'     // Matin
  | 'midday'      // Midi
  | 'afternoon'   // Après-midi
  | 'sunset'      // Coucher du soleil
  | 'dusk'        // Crépuscule
  | 'night';      // Nuit

// Couleurs simplifiées pour chaque mode
const BACKGROUND_MODES = {
  night: {
    primary: '#2c3e50',   // 🔧 CISCO: Bleu moyen pour le bas (horizon nocturne visible)
    secondary: '#151b24', // 🔧 CISCO: Bleu très sombre intermédiaire (plus foncé)
    tertiary: '#050810'   // 🔧 CISCO: Presque noir total pour le haut (nuit profonde accentuée)
  },
  dawn: {
    primary: '#E6F3FF',   // 🔧 CISCO: Bleu clair + rose pastel pour l'horizon
    secondary: '#C8E1F5', // 🔧 CISCO: Bleu clair intermédiaire
    tertiary: '#2C3E50'   // 🔧 CISCO: Haut BEAUCOUP plus sombre
  },
  sunrise: {
    primary: '#FFE4E1',   // 🔧 CISCO: Rose pastel très doux (au lieu d'orange)
    secondary: '#F0C8D4', // 🔧 CISCO: Rose-bleu pastel intermédiaire
    tertiary: '#ADD8E6'   // 🔧 CISCO: Bleu clair (bleu descend plus bas)
  },
  morning: {
    primary: '#F0E68C',   // Jaune khaki
    secondary: '#ADD8E6', // Bleu clair
    tertiary: '#4682B4'   // Bleu acier
  },
  midday: {
    primary: '#87CEEB',   // Bleu ciel
    secondary: '#4682B4', // Bleu acier
    tertiary: '#1E3A8A'   // Bleu profond
  },
  afternoon: {
    primary: '#F0E68C',   // Jaune khaki
    secondary: '#ADD8E6', // Bleu clair
    tertiary: '#4682B4'   // Bleu acier
  },
  sunset: {
    primary: '#FFA07A',   // Saumon pastel
    secondary: '#ADD8E6', // Bleu clair
    tertiary: '#4682B4'   // Bleu acier
  },
  dusk: {
    primary: '#FFD4A3',   // Orange pastel doux
    secondary: '#ADD8E6', // Bleu clair
    tertiary: '#4682B4'   // Bleu acier
  }
};

// 🔧 NOUVEAU: Système de couleurs de transition pour ponts fluides
const TRANSITION_MODES = {
  'night-dawn': {
    primary: '#4A5F7A',   // Pont bleu-gris vers crème
    secondary: '#6B8CAF', // Transition douce
    tertiary: '#8BB3E8'   // Bleu plus clair
  },
  'dawn-sunrise': {
    primary: '#FFE4B5',   // Beige chaud vers orange
    secondary: '#FFD6A5', // Orange très pâle
    tertiary: '#B8D4F1'   // Maintien du bleu ciel
  },
  'sunrise-morning': {
    primary: '#FFD07A',   // Orange vers jaune
    secondary: '#B8E6D2', // Transition vers bleu clair
    tertiary: '#4682B4'   // Bleu acier stable
  },
  'morning-midday': {
    primary: '#BBE6F0',   // Jaune vers bleu ciel
    secondary: '#87CEEB', // Bleu ciel direct
    tertiary: '#4682B4'   // Bleu acier stable
  },
  'midday-afternoon': {
    primary: '#B8E6F0',   // Maintien bleu ciel
    secondary: '#96D4E6', // Légère transition
    tertiary: '#2E4A8A'   // Bleu légèrement plus chaud
  },
  'afternoon-sunset': {
    primary: '#FFCC99',   // Jaune vers saumon
    secondary: '#FFA999', // Saumon plus vif
    tertiary: '#4682B4'   // Bleu acier stable
  },
  'sunset-dusk': {
    primary: '#FFB899',   // Saumon vers orange doux
    secondary: '#FFD0A3', // Orange pastel
    tertiary: '#5695C4'   // Bleu plus doux
  },
  'dusk-night': {
    primary: '#4a5568',   // Gris-bleu doux vers sombre
    secondary: '#2a3544', // Bleu-gris intermédiaire vers nouveau système
    tertiary: '#151d2a'   // Pont vers le bleu-noir dramatique
  }
};

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  skyMode: string;
}



const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, skyMode }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🔧 CISCO: Mode par défaut = 12h (midday) si aucun mode spécifié
  const defaultMode = 'midday';
  const currentModeRef = useRef(skyMode || defaultMode);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🔧 CISCO: sunriseAnimationRef supprimé - géré dans AstronomicalLayer

  // 🔧 CISCO: Background UNIQUE - Background.png seulement (simplification)
  const selectedBackground = '/Background.png'; // Background unique pour simplifier

  // 🔧 CISCO: Position simplifiée pour Background.png unique
  const getBackgroundPosition = (): string => {
    return 'center bottom -200px'; // 🔧 CISCO: Paysage complètement en bas pour MAXIMUM de ciel dégagé et lune visible rapidement
  };
  
  // 🔧 CISCO: SUPPRESSION COMPLÈTE - Plus de fonction automatique basée sur l'heure
  // const getModeForTime = ... // SUPPRIMÉ - Mode manuel uniquement



  // 🔧 CISCO: ANCIEN SYSTÈME SUPPRIMÉ - DiurnalLayer s'occupe maintenant de tout

  // 🔧 CISCO: Fonction pour les transitions d'étoiles - IMPLÉMENTÉE
  const applyStarsTransition = (mode: BackgroundMode, duration: number) => {
    console.log(`⭐ Transition des étoiles vers ${mode} (${duration}s)`);

    // Déclencher la mise à jour du mode dans AstronomicalLayer via l'état
    // Le composant AstronomicalLayer recevra automatiquement le nouveau skyMode
    // et FixedStars se chargera de la transition des étoiles

    // Pas besoin d'action directe ici car le skyMode est passé en props
    // et les useEffect dans FixedStars gèrent les transitions automatiquement
  };



  // 🔧 CISCO: FONCTIONS SPÉCIALISÉES pour chaque mode - Éviter les conflits
  const applyNightMode = () => {
    console.log('🌌 APPLICATION MODE NUIT PROFONDE - Spécialisé');
    const colors = BACKGROUND_MODES.night;
    const gradient = `linear-gradient(to top, ${colors.primary} 50%, ${colors.secondary} 75%, ${colors.tertiary} 100%)`;
    const brightness = 0.15;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyDawnMode = () => {
    console.log('🌅 APPLICATION MODE AUBE - Spécialisé');
    const colors = BACKGROUND_MODES.dawn;
    const gradient = `linear-gradient(to top, ${colors.primary} 25%, ${colors.secondary} 45%, ${colors.tertiary} 100%)`;
    const brightness = 0.4;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 15.0,
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 15.0,
        ease: "power2.inOut"
      });
    }
  };

  const applyMorningMode = () => {
    console.log('🌤️ APPLICATION MODE MATIN - Spécialisé');
    const colors = BACKGROUND_MODES.morning;
    const gradient = `linear-gradient(to top, ${colors.primary} 50%, ${colors.secondary} 75%, ${colors.tertiary} 100%)`;
    const brightness = 0.8;

    if (gradientRef.current) {
      gsap.to(gradientRef.current, {
        backgroundImage: gradient,
        duration: 20.0, // Mode matin = 20s
        ease: "power2.inOut",
        force3D: true
      });
    }

    if (landscapeRef.current) {
      gsap.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: 20.0,
        ease: "power2.inOut"
      });
    }
  };

  // 🔧 CISCO: FONCTION DE RÉINITIALISATION COMPLÈTE pour éviter les conflits
  const resetAllLayers = (targetMode: BackgroundMode) => {
    console.log(`🔄 RÉINITIALISATION COMPLÈTE pour mode: ${targetMode}`);

    // Forcer la mise à jour du mode immédiatement pour que les couches se régénèrent
    currentModeRef.current = targetMode;

    // Appliquer la fonction spécialisée selon le mode
    switch (targetMode) {
      case 'night':
        applyNightMode();
        break;
      case 'dawn':
        applyDawnMode();
        break;
      case 'morning':
        applyMorningMode();
        break;
      default:
        // Pour les autres modes, utiliser la fonction générique
        updateBackgroundSmoothly(targetMode);
        break;
    }

    // Les useEffect des couches (DiurnalLayer, AstronomicalLayer) vont se déclencher
    // automatiquement grâce au changement de skyMode en props
  };

  // 🔧 CISCO: Changement de mode avec CROSS FADE progressif TOUJOURS
  const setBackgroundMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter TOUTES les animations en cours
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation en cours pour nouvelle transition');
      timelineRef.current.kill();
      setIsTransitioning(false); // 🔧 CISCO: Forcer la réinitialisation du flag
    }

    // 🔧 CISCO: PROTECTION ANTI-BLOCAGE - Forcer déblocage après 20s max
    if (isTransitioning) {
      console.log('⏳ Transition en cours, vérification anti-blocage...');
      setTimeout(() => {
        if (isTransitioning) {
          console.log('🔓 DÉBLOCAGE FORCÉ - Transition bloquée > 20s');
          setIsTransitioning(false);
          setBackgroundMode(mode); // Relancer la transition
        }
      }, 20000);
      return;
    }

    // Si c'est le même mode, ne rien faire (évite le spam de logs)
    if (mode === currentModeRef.current) {
      console.log('🔄 Mode identique, pas de transition');
      return;
    }

    console.log(`🎨 Changement de mode vers: ${mode} depuis ${currentModeRef.current}`);

    // 🔧 CISCO: RÉINITIALISATION FORCÉE pour modes non adjacents
    const transitionKey = `${currentModeRef.current}-${mode}` as keyof typeof TRANSITION_MODES;
    const isAdjacentTransition = TRANSITION_MODES[transitionKey];

    if (!isAdjacentTransition) {
      console.log(`🔄 TRANSITION NON ADJACENTE détectée: ${currentModeRef.current} → ${mode} - RÉINITIALISATION FORCÉE`);
      resetAllLayers(mode);
    }

    // Transition avec pont si modes adjacents
    if (isAdjacentTransition) {
      console.log(`🌉 Utilisation du pont de transition: ${transitionKey}`);
      // Appliquer d'abord la couleur de transition
      updateBackgroundWithBridge(mode, transitionKey);
    } else {
      // 🔧 AMÉLIORATION: Transition directe mais DOUCE pour modes non adjacents
      console.log(`🎨 Transition directe douce vers: ${mode}`);
      // ✅ CORRECTION: Ne pas changer currentMode immédiatement pour éviter le changement brutal
      updateBackgroundSmoothly(mode); // Cette fonction se chargera de mettre à jour le mode
    }
  };

  // 🔧 NOUVELLE FONCTION: Transition douce même pour les modes non adjacents
  const updateBackgroundSmoothly = (targetMode: BackgroundMode) => {
    if (!gradientRef.current) return;

    const targetColors = getColorsForMode(targetMode);
    
    setIsTransitioning(true);
    
    // Créer le dégradé final avec transition ultra douce
    let finalGradient;
    if (targetMode === 'dawn') {
      finalGradient = `linear-gradient(to top, ${targetColors.primary} 25%, ${targetColors.secondary} 45%, ${targetColors.tertiary} 100%)`;
    } else {
      finalGradient = `linear-gradient(to top, ${targetColors.primary} 50%, ${targetColors.secondary} 75%, ${targetColors.tertiary} 100%)`;
    }
    
    const targetBrightness = getBrightnessForMode(targetMode);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode;
        console.log(`✨ Transition douce vers ${targetMode} terminée !`);
      }
    });

    // 🌊 CISCO: SYNCHRONISATION PARFAITE - 15 secondes avec easing très doux
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 25.0, // 🔧 CISCO: Harmonisation à 25 secondes pour synchronisation avec soleil
      ease: "power1.inOut", // Easing plus doux que power2
      force3D: true,
      willChange: "background-image"
    });

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - Synchronisée (durée adaptée selon le mode)
    const transitionDuration = targetMode === 'morning' ? 30.0 : 25.0; // 🔧 CISCO: Mode matin = 30s, autres = 25s
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration, // 🔧 CISCO: Harmonisation adaptée
        ease: "power1.inOut"
      }, 0);
    }

  };

  // 🔧 NOUVELLE FONCTION: Transition avec pont intermédiaire
  const updateBackgroundWithBridge = (targetMode: BackgroundMode, transitionKey: keyof typeof TRANSITION_MODES) => {
    if (!gradientRef.current) return;

    const bridgeColors = TRANSITION_MODES[transitionKey];
    const targetColors = getColorsForMode(targetMode);
    
    setIsTransitioning(true);
    
    // Créer le dégradé de pont
    const bridgeGradient = `linear-gradient(to top, ${bridgeColors.primary} 50%, ${bridgeColors.secondary} 75%, ${bridgeColors.tertiary} 100%)`;
    
    // Créer le dégradé final
    let finalGradient;
    if (targetMode === 'dawn') {
      finalGradient = `linear-gradient(to top, ${targetColors.primary} 25%, ${targetColors.secondary} 45%, ${targetColors.tertiary} 100%)`;
    } else {
      finalGradient = `linear-gradient(to top, ${targetColors.primary} 50%, ${targetColors.secondary} 75%, ${targetColors.tertiary} 100%)`;
    }
    
    const targetBrightness = getBrightnessForMode(targetMode);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        currentModeRef.current = targetMode;
        setIsTransitioning(false);
        console.log(`✨ Transition avec pont vers ${targetMode} terminée !`);
      }
    });

    // 🌉 CISCO: PHASE 1 - Transition vers le pont (7.5 secondes)
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: bridgeGradient,
      duration: 7.5, // CISCO: Première moitié des 15 secondes
      ease: "power1.inOut",
      force3D: true,
      willChange: "background-image"
    });

    // 🌉 CISCO: PHASE 2 - Transition du pont vers le mode final (7.5 secondes)
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 7.5, // CISCO: Seconde moitié des 15 secondes
      ease: "power1.inOut",
      force3D: true,
      willChange: "background-image"
    }, 7.5); // CISCO: Démarre après 7.5 secondes

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - Synchronisée (durée adaptée selon le mode)
    const transitionDuration = targetMode === 'morning' ? 30.0 : 25.0; // 🔧 CISCO: Mode matin = 30s, autres = 25s
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration, // 🔧 CISCO: Harmonisation adaptée
        ease: "power1.inOut"
      }, 0);
    }

    // 🔧 CISCO: NUAGES GÉRÉS PAR DiurnalLayer - Plus besoin d'intervention manuelle

    // 🔧 CISCO: SYNCHRONISATION DES ÉTOILES - MÊME TIMING QUE LES NUAGES
    timelineRef.current.call(() => {
      applyStarsTransition(targetMode, transitionDuration);
    }, [], 0.1); // 🔧 CISCO: Même délai pour synchronisation parfaite
  };

  // 🔧 FONCTION SIMPLIFIÉE: Obtenir les couleurs pour un mode donné
  const getColorsForMode = (mode: BackgroundMode) => {
    return BACKGROUND_MODES[mode];
  };

  // 🔧 FONCTION SIMPLIFIÉE: Calculer l'éclairage selon le mode
  const getBrightnessForMode = (mode: BackgroundMode): number => {
    switch (mode) {
      case 'night': return 0.15; // 🔧 CISCO: Réduit de 0.2 à 0.15 pour moins d'éclairage en nuit profonde
      case 'dawn': return 0.4;
      case 'sunrise': return 0.6;
      case 'morning': return 0.8;
      case 'midday': return 1.0;
      case 'afternoon': return 0.8;
      case 'sunset': return 0.6;
      case 'dusk': return 0.4;
      default: return 0.6;
    }
  };


  // 🔧 FONCTION PRINCIPALE: Transition progressive fluide entre modes
  const updateDynamicBackground = (mode?: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter animations actives avant nouvelle transition
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation active dans updateDynamicBackground');
      timelineRef.current.kill();
    }

    if (isTransitioning) { console.log('⏳ Transition déjà en cours, updateDynamicBackground ignoré'); return; }
    if (!gradientRef.current) return;

    const targetMode = mode || skyMode as BackgroundMode;
    const colors = getColorsForMode(targetMode);
    
    // 🎬 INDICATEUR DE TRANSITION
    setIsTransitioning(true);

    // 🔧 DÉGRADÉ MODIFIÉ: Pour l'aube - bleu descend beaucoup plus bas
    let gradient;
    if (targetMode === 'dawn') {
      gradient = `linear-gradient(to top, ${colors.primary} 25%, ${colors.secondary} 45%, ${colors.tertiary} 100%)`;
    } else {
      // Autres modes - commence au milieu de l'écran (50%)
      gradient = `linear-gradient(to top, ${colors.primary} 50%, ${colors.secondary} 75%, ${colors.tertiary} 100%)`;
    }
    
    const brightness = getBrightnessForMode(targetMode);

    console.log(`🎨 Transition progressive fluide vers ${targetMode}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode; // ✅ Mettre à jour le mode courant pour éviter toute re-boucle
        console.log(`✨ Transition vers ${targetMode} terminée !`);
      }
    });

    // 🌅 CISCO: TRANSITION DIRECTE - Changement progressif des couleurs (15 secondes)
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: gradient,
      duration: 25.0, // 🔧 CISCO: Harmonisation à 25 secondes
      ease: "power2.inOut",
      force3D: true,
      willChange: "background-image"
    });

    // ✨ CISCO: TRANSITION DE L'ÉCLAIRAGE - Synchronisée et progressive (durée adaptée)
    const transitionDuration = targetMode === 'morning' ? 30.0 : 25.0; // 🔧 CISCO: Mode matin = 30s, autres = 25s
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: transitionDuration, // 🔧 CISCO: Harmonisation adaptée
        ease: "power2.inOut"
      }, 0);
    }

  };

  // Animation de zoom du paysage
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🔧 CISCO: Fonctions soleil supprimées - gérées dans AstronomicalLayer

  // 🌌 CISCO: Fonction SIMPLIFIÉE pour nuit profonde - Dégradé seulement
  const triggerNightAnimation = () => {
    console.log('🌌 DÉCLENCHEMENT NUIT PROFONDE - Dégradé seulement');
    updateDynamicBackground('night');
  };

  // Exposer les fonctions globalement
  (window as any).triggerNightAnimation = triggerNightAnimation; // CISCO: Animation nuit profonde

  // Initialisation une seule fois
  useEffect(() => {
    if (gradientRef.current) {
      const initialColors = getColorsForMode(skyMode as BackgroundMode);
      // 🔧 CISCO: Dégradé initial adapté selon le mode
      let initialGradient;
      if (skyMode === 'dawn') {
        initialGradient = `linear-gradient(to top, ${initialColors.primary} 25%, ${initialColors.secondary} 45%, ${initialColors.tertiary} 100%)`;
      } else {
        initialGradient = `linear-gradient(to top, ${initialColors.primary} 50%, ${initialColors.secondary} 75%, ${initialColors.tertiary} 100%)`;
      }
      gsap.set(gradientRef.current, {
        backgroundImage: initialGradient
      });
    }

    createLandscapeZoomAnimation();
    updateDynamicBackground();

    // 🔧 CISCO: Initialiser l'éclairage du paysage pour le mode par défaut (midday)
    if (landscapeRef.current) {
      const initialBrightness = getBrightnessForMode('midday');
      gsap.set(landscapeRef.current, {
        filter: `brightness(${initialBrightness})`
      });
      console.log(`💡 Éclairage paysage initialisé pour midday: brightness(${initialBrightness})`);
    }

    return () => {
      if (timelineRef.current) timelineRef.current.kill();
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  // 🔧 CISCO: Mode manuel UNIQUEMENT - Initialisation par défaut
  useEffect(() => {
    if (skyMode) {
      // Mode manuel via props
      console.log(`🎯 Mode manuel détecté: ${skyMode}`);
      if (skyMode !== (currentModeRef.current as string)) {
        setBackgroundMode(skyMode as BackgroundMode);
      }
    } else {
      // 🔧 CISCO: Mode par défaut = 12h (midday) au chargement
      console.log('🌅 Initialisation mode par défaut: midday (12h)');
      setBackgroundMode(defaultMode as BackgroundMode);
    }
  }, [skyMode]);



  // 🔧 CISCO: Exposer la fonction de changement de mode pour le contrôleur
  useEffect(() => {
    (window as any).setBackgroundMode = (mode: string) => {
      console.log(`🎨 Changement de mode via contrôleur: ${mode}`);
      setBackgroundMode(mode as BackgroundMode);
    };

    return () => {
      delete (window as any).setBackgroundMode;
    };
  }, []);

  return (
    <div
      ref={backgroundRef}
      className="relative overflow-hidden"
      style={{ minHeight: '100vh' }}
    >
      {/* Conteneur pour le dégradé - commence plus haut pour l'aube */}
      <div
        ref={gradientRef}
        className="absolute inset-0"
        style={{
          zIndex: 0,
          backgroundAttachment: 'fixed',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
          // ✅ CORRECTION: Supprimer le fallbackGradient qui entre en conflit avec GSAP
        }}
      />

      {/* Couches avec nuages réduits - Mode passé aux étoiles */}
      <AstronomicalLayer skyMode={skyMode as BackgroundMode} />
      <DiurnalLayer skyMode={skyMode as BackgroundMode} />

      {/* 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer */}

      {/* Paysage avec éclairage dynamique - Background aléatoire */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: `url(${selectedBackground})`,
          backgroundPosition: getBackgroundPosition(), // Position pour Background.png
          backgroundSize: 'cover', // Taille standard pour tous les backgrounds
          zIndex: 10, // 🔧 CISCO: Paysage en avant-plan (z-index 10)
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* Contenu principal */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* Indicateur de transition */}
      {isTransitioning && (
        <div className="fixed top-4 right-4 bg-[#0D9488]/90 text-white px-4 py-2 rounded-lg backdrop-blur-sm z-50 shadow-lg border border-[#A550F5]/30">
          <div className="flex items-center gap-2">
            <div className="animate-pulse">
              ✨
            </div>
            <span className="text-sm font-medium">
              Transition...
            </span>
          </div>
        </div>
      )}

      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
