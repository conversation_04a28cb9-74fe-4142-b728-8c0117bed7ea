import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  isNightMode: boolean; // true quand mode = 'night'
  currentMode: string;  // pour détecter les changements de mode
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ isNightMode, currentMode }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Timeline | null>(null); // 🔧 CISCO: Correction type Timeline
  const isAnimatingRef = useRef<boolean>(false); // 🔧 CISCO: Protection contre les déclenchements multiples
  const hasAnimatedRef = useRef<boolean>(false); // 🔧 CISCO: Empêcher les re-animations

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🔧 CISCO: DÉBOGAGE - Tracer tous les déclenchements du useEffect
    console.log(`🌙 MoonAnimation useEffect déclenché: isNightMode=${isNightMode}, currentMode=${currentMode}, isAnimating=${isAnimatingRef.current}`);

    // 🌙 CISCO: Mode Nuit profonde - Apparition et descente de la lune
    if (isNightMode && currentMode === 'night') {
      // 🔧 CISCO: PROTECTION ABSOLUE - Une seule animation par session
      if (hasAnimatedRef.current) {
        console.log('🌙 Animation lune déjà effectuée cette session - AUCUNE répétition');
        return;
      }

      // 🔧 CISCO: PROTECTION RENFORCÉE - Éviter les déclenchements multiples
      if (isAnimatingRef.current) {
        console.log('🌙 Animation lune déjà en cours (protection renforcée) - éviter le redémarrage');
        return;
      }

      // 🔧 CISCO: Vérifier si l'animation GSAP est déjà active
      if (animationRef.current && animationRef.current.isActive()) {
        console.log('🌙 Animation GSAP lune déjà active - éviter le redémarrage');
        return;
      }

      console.log('🌙 DÉMARRAGE animation lune - Mode nuit profonde confirmé - PREMIÈRE FOIS');
      isAnimatingRef.current = true; // 🔧 CISCO: Marquer comme en cours d'animation
      hasAnimatedRef.current = true; // 🔧 CISCO: Marquer comme déjà animé

      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // 🔧 CISCO: Position initiale de la lune - gauche HAUT de l'écran pour apparition rapide
      gsap.set(moonRef.current, {
        x: '5vw', // Gauche pour commencer la trajectoire parabolique
        y: '35vh', // 🔧 CISCO: REMONTÉE de 60vh à 35vh pour apparition en 5-10s
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Position initiale du halo (même position que la lune)
      gsap.set(haloRef.current, {
        x: '5vw',
        y: '60vh',
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        display: 'block'
      });

      // 🔧 CISCO: Timeline SANS DÉLAI - Animation immédiate
      animationRef.current = gsap.timeline({
        onComplete: () => {
          console.log('🌙 Animation lune terminée - Libération du verrou');
          isAnimatingRef.current = false; // 🔧 CISCO: Libérer le verrou à la fin
        }
      });

      // 🔧 CISCO: Position initiale VISIBLE - Lune commence directement visible
      gsap.set(moonRef.current, { opacity: 1.0 });
      gsap.set(haloRef.current, { opacity: 0.25 });

      // 🌙 CISCO: NOUVELLE TRAJECTOIRE PARABOLIQUE - Gauche-haut → Zénith → Droite-milieu
      animationRef.current.to([moonRef.current, haloRef.current], {
        keyframes: [
          // Phase 1: Montée parabolique du gauche-haut vers le zénith
          { x: '5vw', y: '35vh', duration: 0 },     // 🔧 CISCO: Départ gauche-haut (35vh au lieu de 60vh)
          { x: '15vw', y: '25vh', duration: 0.15 }, // 🔧 CISCO: Montée progressive plus haute
          { x: '25vw', y: '18vh', duration: 0.25 }, // 🔧 CISCO: Continue la montée plus haute
          { x: '35vw', y: '12vh', duration: 0.35 }, // 🔧 CISCO: Approche du zénith plus haute
          { x: '45vw', y: '8vh', duration: 0.45 },  // 🔧 CISCO: Proche du zénith plus haute
          { x: '50vw', y: '5vh', duration: 0.5 },   // 🔧 CISCO: ZÉNITH plus haut (5vh au lieu de 10vh)
          // Phase 2: Descente parabolique du zénith vers droite-milieu
          { x: '55vw', y: '8vh', duration: 0.55 },  // 🔧 CISCO: Début descente plus haute
          { x: '65vw', y: '12vh', duration: 0.65 }, // 🔧 CISCO: Descente progressive plus haute
          { x: '75vw', y: '18vh', duration: 0.75 }, // 🔧 CISCO: Continue la descente plus haute
          { x: '85vw', y: '25vh', duration: 0.85 }, // 🔧 CISCO: Approche finale plus haute
          { x: '95vw', y: '35vh', duration: 1.0 }   // 🔧 CISCO: Arrivée droite-haut (35vh au lieu de 60vh)
        ],
        duration: 900, // 🔧 CISCO: 15 minutes - mouvement naturel et lent
        ease: "power2.inOut", // Courbe parabolique naturelle
        transformOrigin: "center center"
      }); // 🔧 CISCO: Animation immédiate, trajectoire en cloche

    } else if (!isNightMode && currentMode !== 'night') {
      console.log('🌙 Mode non-nuit détecté - Arrêt et disparition de la lune');

      // 🔧 CISCO: Libérer TOUS les verrous d'animation
      isAnimatingRef.current = false;
      hasAnimatedRef.current = false; // 🔧 CISCO: CORRECTION - Permettre nouvelle animation si retour mode nuit

      // Arrêter l'animation de descente
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Si la lune est visible, la faire disparaître en douceur avec le halo
      if (
        moonRef.current &&
        Number(gsap.getProperty(moonRef.current, "opacity")) > 0
      ) {
        fadeOutRef.current = gsap.timeline();

        // Disparition de la lune
        fadeOutRef.current.to(moonRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in"
        });

        // Disparition du halo en parallèle
        fadeOutRef.current.to(haloRef.current, {
          opacity: 0,
          duration: 8,
          ease: "power2.in",
          onComplete: () => {
            if (moonRef.current && haloRef.current) {
              gsap.set(moonRef.current, { display: 'none' });
              gsap.set(haloRef.current, { display: 'none' });
            }
          }
        }, 0); // En même temps que la lune
      } else {
        // Si déjà invisible, juste les cacher
        gsap.set(moonRef.current, { display: 'none' });
        gsap.set(haloRef.current, { display: 'none' });
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [isNightMode, currentMode]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 30%, rgba(255, 255, 255, 0.04) 60%, transparent 100%)', // 🔧 CISCO: Halo plus lumineux
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />

      {/* 🌙 CISCO: Lune principale */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          filter: 'brightness(1.6) contrast(1.3)', // 🔧 CISCO: Plus lumineuse (était 1.3/1.1)
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
